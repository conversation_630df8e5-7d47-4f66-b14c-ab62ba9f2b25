use shredstream_decoder::types::{Entry, VersionedTransaction, VersionedMessage, LegacyMessage, V0Message, MessageHeader, CompiledInstruction, Pubkey, MessageAddressTableLookup};
use solana_hash::Hash;
use solana_signature::Signature;
use std::fs;
use std::path::Path;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔧 Generating test data for binary compatibility testing...");
    
    let output_dir = "tests/data/raw_shreds";
    fs::create_dir_all(output_dir)?;
    
    let target_count = 100; // Generate 100 sample files for testing
    
    for i in 1..=target_count {
        let entry = generate_mock_entry_with_transactions(i % 5); // 0-4 transactions per entry
        let serialized = bincode::serialize(&entry)?;
        
        let filename = format!("shred_{:06}.bin", i);
        let filepath = Path::new(output_dir).join(&filename);
        
        fs::write(&filepath, &serialized)?;
        
        if i % 10 == 0 {
            println!("Generated {} sample files...", i);
        }
    }
    
    // Generate some edge cases
    let edge_cases = vec![
        generate_empty_entry(),
        generate_single_transaction_entry(),
        generate_max_size_entry(),
    ];
    
    for (i, entry) in edge_cases.iter().enumerate() {
        let serialized = bincode::serialize(entry)?;
        let filename = format!("edge_case_{:03}.bin", i + 1);
        let filepath = Path::new(output_dir).join(&filename);
        fs::write(&filepath, &serialized)?;
    }
    
    println!("✅ Generated {} sample files + 3 edge cases in {}/", target_count, output_dir);
    println!("📊 Data ready for binary compatibility testing!");
    
    Ok(())
}

fn generate_mock_entry_with_transactions(tx_count: usize) -> Entry {
    let mut transactions = Vec::new();
    
    for i in 0..tx_count {
        transactions.push(generate_mock_versioned_transaction_with_index(i));
    }

    Entry {
        num_hashes: 1,
        hash: generate_mock_hash_with_seed(42),
        transactions,
    }
}

fn generate_mock_versioned_transaction_with_index(index: usize) -> VersionedTransaction {
    let seed = (index * 17 + 31) as u64;
    VersionedTransaction {
        signatures: vec![generate_mock_signature_with_seed(seed)],
        message: if index % 2 == 0 {
            VersionedMessage::Legacy(generate_mock_legacy_message_with_seed(seed))
        } else {
            VersionedMessage::V0(generate_mock_v0_message_with_seed(seed))
        },
    }
}

fn generate_mock_legacy_message_with_seed(seed: u64) -> LegacyMessage {
    LegacyMessage {
        header: MessageHeader {
            num_required_signatures: 1,
            num_readonly_signed_accounts: 0,
            num_readonly_unsigned_accounts: 0,
        },
        account_keys: vec![generate_mock_pubkey_with_seed(seed)],
        recent_blockhash: generate_mock_hash_with_seed(seed + 100),
        instructions: vec![generate_mock_compiled_instruction_with_seed(seed + 200)],
    }
}

fn generate_mock_v0_message_with_seed(seed: u64) -> V0Message {
    V0Message {
        header: MessageHeader {
            num_required_signatures: 1,
            num_readonly_signed_accounts: 0,
            num_readonly_unsigned_accounts: 1,
        },
        account_keys: vec![generate_mock_pubkey_with_seed(seed + 10)],
        recent_blockhash: generate_mock_hash_with_seed(seed + 110),
        instructions: vec![generate_mock_compiled_instruction_with_seed(seed + 210)],
        address_table_lookups: vec![generate_mock_address_table_lookup_with_seed(seed + 300)],
    }
}

fn generate_empty_entry() -> Entry {
    Entry {
        num_hashes: 0,
        hash: Hash::default(),
        transactions: vec![],
    }
}

fn generate_single_transaction_entry() -> Entry {
    Entry {
        num_hashes: 1,
        hash: generate_mock_hash_with_seed(999),
        transactions: vec![generate_mock_versioned_transaction_with_index(0)],
    }
}

fn generate_max_size_entry() -> Entry {
    let mut transactions = Vec::new();
    for i in 0..10 { // Reduced from 100 to 10 for reasonable file size
        transactions.push(generate_mock_versioned_transaction_with_index(i));
    }

    Entry {
        num_hashes: 1000000,
        hash: generate_mock_hash_with_seed(u64::MAX),
        transactions,
    }
}

fn generate_mock_compiled_instruction_with_seed(seed: u64) -> CompiledInstruction {
    let data_len = (seed % 10 + 1) as usize;
    let mut data = Vec::new();
    for i in 0..data_len {
        data.push(((seed + i as u64) % 256) as u8);
    }

    CompiledInstruction {
        program_id_index: (seed % 256) as u8,
        accounts: vec![(seed % 256) as u8, ((seed + 1) % 256) as u8],
        data,
    }
}

fn generate_mock_pubkey_with_seed(seed: u64) -> Pubkey {
    let mut bytes = [0u8; 32];
    let seed_bytes = seed.to_le_bytes();
    for i in 0..4 {
        bytes[i * 8..(i + 1) * 8].copy_from_slice(&seed_bytes);
    }
    Pubkey::new_from_array(bytes)
}

fn generate_mock_hash_with_seed(seed: u64) -> Hash {
    let mut bytes = [0u8; 32];
    let seed_bytes = seed.to_le_bytes();
    for i in 0..4 {
        bytes[i * 8..(i + 1) * 8].copy_from_slice(&seed_bytes);
    }
    Hash::new_from_array(bytes)
}

fn generate_mock_signature_with_seed(seed: u64) -> Signature {
    let mut bytes = [0u8; 64];
    let seed_bytes = seed.to_le_bytes();
    for i in 0..8 {
        bytes[i * 8..(i + 1) * 8].copy_from_slice(&seed_bytes);
    }
    Signature::from(bytes)
}

fn generate_mock_address_table_lookup_with_seed(seed: u64) -> MessageAddressTableLookup {
    let writable_count = (seed % 5 + 1) as usize;
    let readonly_count = (seed % 3 + 1) as usize;

    let mut writable_indexes = Vec::new();
    for i in 0..writable_count {
        writable_indexes.push(((seed + i as u64) % 256) as u8);
    }

    let mut readonly_indexes = Vec::new();
    for i in 0..readonly_count {
        readonly_indexes.push(((seed + 100 + i as u64) % 256) as u8);
    }

    MessageAddressTableLookup {
        account_key: generate_mock_pubkey_with_seed(seed + 1000),
        writable_indexes,
        readonly_indexes,
    }
}

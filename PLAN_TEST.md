# 📋 KẾ HOẠCH TESTING TOÀN DIỆN - SHREDSTREAM DECODER

## 🎯 MỤC TIÊU CHÍNH

Thiết lập hệ thống testing toàn diện để đảm bảo **100% Binary Compatibility** gi<PERSON><PERSON> các custom types và official Solana crates, sử dụng real-world data từ shredstream để validation.

---

# 📋 KẾ HOẠCH CHI TIẾT: HỆ THỐNG TESTING 100% BINARY COMPATIBILITY

## 🔍 PHÂN TÍCH CODEBASE HIỆN TẠI

### **Main Library Functions:**

-   `decode_entries(slot: u64, bytes: &[u8]) -> Result<ParsedEntry, JsValue>` - Function chính cần test

### **Custom Types cần test 100% binary compatibility:**

1. **`src/types/entries.rs`**: `Entry`, `ParsedEntry`
2. **`src/types/accounts.rs`**: `Pubkey`, `MessageAddressTableLookup`
3. **`src/types/messages.rs`**: `VersionedMessage`, `LegacyMessage`, `V0Message`, `MessageHeader`
4. **`src/types/instructions.rs`**: `CompiledInstruction`
5. **`src/types/transactions.rs`**: `VersionedTransaction`
6. **`src/utils/message_deserializer.rs`**: `MessageVisitor`, `MessagePrefix`

### **Official Solana Crates Available (từ dev-dependencies):**

-   `solana-entry` v2.2.7
-   `solana-transaction` v2.2.2
-   `solana-message` v2.2.1
-   `solana-pubkey` v2.2.1

---

## 🏗️ 1. HELPER FUNCTIONS TRONG `tests/common/`

### **A. `tests/common/reference_implementations.rs`**

```rust
// Reference implementations using official Solana crates
pub mod reference {
    use solana_entry::Entry as SolanaEntry;
    use solana_transaction::VersionedTransaction as SolanaVersionedTransaction;
    use solana_message::{VersionedMessage as SolanaVersionedMessage, Message as SolanaMessage};
    use solana_pubkey::Pubkey as SolanaPubkey;

    // Reference function using official solana-entry crate
    pub fn decode_entries_reference(bytes: &[u8]) -> Result<Vec<SolanaEntry>, bincode::Error>;

    // Reference serialization functions
    pub fn serialize_entry_reference(entry: &SolanaEntry) -> Result<Vec<u8>, bincode::Error>;
    pub fn serialize_transaction_reference(tx: &SolanaVersionedTransaction) -> Result<Vec<u8>, bincode::Error>;
    pub fn serialize_message_reference(msg: &SolanaVersionedMessage) -> Result<Vec<u8>, bincode::Error>;

    // Reference deserialization functions
    pub fn deserialize_entry_reference(bytes: &[u8]) -> Result<SolanaEntry, bincode::Error>;
    pub fn deserialize_transaction_reference(bytes: &[u8]) -> Result<SolanaVersionedTransaction, bincode::Error>;
    pub fn deserialize_message_reference(bytes: &[u8]) -> Result<SolanaVersionedMessage, bincode::Error>;
}
```

### **B. `tests/common/compatibility_validators.rs`**

```rust
// Binary compatibility validation functions
pub mod validators {
    // Byte-by-byte comparison functions
    pub fn validate_entry_binary_compatibility(custom_bytes: &[u8], reference_bytes: &[u8]) -> bool;
    pub fn validate_transaction_binary_compatibility(custom_bytes: &[u8], reference_bytes: &[u8]) -> bool;
    pub fn validate_message_binary_compatibility(custom_bytes: &[u8], reference_bytes: &[u8]) -> bool;

    // Structural comparison functions
    pub fn validate_entry_structure_compatibility(custom: &crate::types::Entry, reference: &solana_entry::Entry) -> bool;
    pub fn validate_transaction_structure_compatibility(custom: &crate::types::VersionedTransaction, reference: &solana_transaction::VersionedTransaction) -> bool;

    // Hash comparison functions
    pub fn validate_hash_compatibility(custom_hash: &solana_hash::Hash, reference_hash: &solana_hash::Hash) -> bool;
    pub fn validate_signature_compatibility(custom_sig: &solana_signature::Signature, reference_sig: &solana_signature::Signature) -> bool;
}
```

### **C. `tests/common/test_data_loader.rs`**

```rust
// Real-world test data management
pub mod data_loader {
    use std::path::Path;

    pub struct ShredDataset {
        pub files: Vec<ShredFile>,
        pub total_samples: usize,
    }

    pub struct ShredFile {
        pub path: PathBuf,
        pub slot: u64,
        pub size: usize,
        pub data: Vec<u8>,
    }

    // Load all shred files from tests/data/
    pub fn load_shred_dataset() -> Result<ShredDataset, std::io::Error>;

    // Load specific shred file
    pub fn load_shred_file(path: &Path) -> Result<ShredFile, std::io::Error>;

    // Get sample data for testing (stratified sampling)
    pub fn get_test_samples(dataset: &ShredDataset, sample_size: usize) -> Vec<&ShredFile>;

    // Get edge case samples (smallest, largest, specific patterns)
    pub fn get_edge_case_samples(dataset: &ShredDataset) -> Vec<&ShredFile>;
}
```

### **D. `tests/common/mock_data.rs`**

```rust
// Mock data generators for controlled testing
pub mod generators {
    // Generate mock entries with various characteristics
    pub fn generate_mock_entry_with_transactions(tx_count: usize) -> crate::types::Entry;
    pub fn generate_mock_legacy_message() -> crate::types::LegacyMessage;
    pub fn generate_mock_v0_message() -> crate::types::V0Message;
    pub fn generate_mock_versioned_transaction() -> crate::types::VersionedTransaction;

    // Generate edge case data
    pub fn generate_empty_entry() -> crate::types::Entry;
    pub fn generate_max_size_entry() -> crate::types::Entry;
    pub fn generate_single_transaction_entry() -> crate::types::Entry;

    // Generate problematic data for robustness testing
    pub fn generate_malformed_data_samples() -> Vec<Vec<u8>>;
}
```

### **E. `tests/common/assertions.rs`**

```rust
// Custom assertion macros for binary compatibility
#[macro_export]
macro_rules! assert_binary_compatible {
    ($custom:expr, $reference:expr) => {
        assert_eq!($custom, $reference, "Binary compatibility failed: custom != reference");
    };
}

#[macro_export]
macro_rules! assert_serialization_compatible {
    ($custom_type:expr, $reference_type:expr) => {
        let custom_bytes = bincode::serialize(&$custom_type).unwrap();
        let reference_bytes = bincode::serialize(&$reference_type).unwrap();
        assert_eq!(custom_bytes, reference_bytes, "Serialization compatibility failed");
    };
}

#[macro_export]
macro_rules! assert_deserialization_compatible {
    ($bytes:expr, $custom_type:ty, $reference_type:ty) => {
        let custom_result: Result<$custom_type, _> = bincode::deserialize($bytes);
        let reference_result: Result<$reference_type, _> = bincode::deserialize($bytes);

        match (custom_result, reference_result) {
            (Ok(custom), Ok(reference)) => {
                // Compare serialized forms to ensure identical binary representation
                let custom_reserialized = bincode::serialize(&custom).unwrap();
                let reference_reserialized = bincode::serialize(&reference).unwrap();
                assert_eq!(custom_reserialized, reference_reserialized, "Deserialization compatibility failed");
            },
            (Err(_), Err(_)) => {}, // Both failed - acceptable
            _ => panic!("Deserialization compatibility failed: one succeeded, one failed"),
        }
    };
}
```

### **F. `tests/common/fixtures.rs`**

```rust
// Static test fixtures and organized test data
pub mod fixtures {
    use lazy_static::lazy_static;

    lazy_static! {
        // Pre-loaded test datasets
        pub static ref SHRED_DATASET: ShredDataset = load_shred_dataset().expect("Failed to load shred dataset");
        pub static ref SAMPLE_ENTRIES: Vec<Vec<u8>> = load_sample_entries();
        pub static ref EDGE_CASE_DATA: Vec<Vec<u8>> = load_edge_case_data();
    }

    // Known good data samples for regression testing
    pub fn get_known_good_entry_bytes() -> &'static [u8];
    pub fn get_known_good_transaction_bytes() -> &'static [u8];
    pub fn get_known_good_message_bytes() -> &'static [u8];

    // Test data categories
    pub fn get_small_entries() -> Vec<&'static [u8]>;
    pub fn get_large_entries() -> Vec<&'static [u8]>;
    pub fn get_complex_entries() -> Vec<&'static [u8]>;
}
```

---

## 🧪 2. TEST CASES CHO TỪNG MODULE

### **A. `tests/units/lib_test.rs`**

```rust
// Unit tests for main decode_entries function
mod decode_entries_tests {
    // Basic functionality tests
    #[test] fn test_decode_entries_basic_functionality();
    #[test] fn test_decode_entries_empty_data();
    #[test] fn test_decode_entries_invalid_data();

    // Binary compatibility tests
    #[test] fn test_decode_entries_binary_compatibility_with_reference();
    #[test] fn test_decode_entries_serialization_roundtrip();

    // Real-world data tests
    #[test] fn test_decode_entries_with_real_shred_data();
    #[test] fn test_decode_entries_with_edge_case_data();

    // Performance and stress tests
    #[test] fn test_decode_entries_large_dataset();
    #[test] fn test_decode_entries_malformed_data_handling();
}
```

### **B. `tests/units/types/entries_test.rs`**

```rust
mod entry_compatibility_tests {
    // Serialization compatibility
    #[test] fn test_entry_serialization_matches_solana_entry();
    #[test] fn test_entry_deserialization_matches_solana_entry();
    #[test] fn test_entry_roundtrip_compatibility();

    // Field-by-field compatibility
    #[test] fn test_entry_num_hashes_compatibility();
    #[test] fn test_entry_hash_compatibility();
    #[test] fn test_entry_transactions_compatibility();

    // Edge cases
    #[test] fn test_entry_empty_transactions();
    #[test] fn test_entry_max_transactions();
    #[test] fn test_entry_zero_hashes();
}

mod parsed_entry_tests {
    #[test] fn test_parsed_entry_structure_compatibility();
    #[test] fn test_parsed_entry_slot_handling();
    #[test] fn test_parsed_entry_entries_array_compatibility();
}
```

### **C. `tests/units/types/transactions_test.rs`**

```rust
mod versioned_transaction_tests {
    // Binary compatibility with solana-transaction
    #[test] fn test_versioned_transaction_serialization_compatibility();
    #[test] fn test_versioned_transaction_deserialization_compatibility();

    // Signature handling
    #[test] fn test_signatures_array_compatibility();
    #[test] fn test_empty_signatures_handling();
    #[test] fn test_multiple_signatures_handling();

    // Message compatibility
    #[test] fn test_message_field_compatibility();
    #[test] fn test_legacy_message_in_transaction();
    #[test] fn test_v0_message_in_transaction();
}
```

### **D. `tests/units/types/messages_test.rs`**

```rust
mod versioned_message_tests {
    // Enum variant compatibility
    #[test] fn test_legacy_message_variant_compatibility();
    #[test] fn test_v0_message_variant_compatibility();

    // Serialization format compatibility
    #[test] fn test_message_version_prefix_handling();
    #[test] fn test_legacy_message_serialization_format();
    #[test] fn test_v0_message_serialization_format();
}

mod legacy_message_tests {
    #[test] fn test_legacy_message_header_compatibility();
    #[test] fn test_legacy_message_account_keys_compatibility();
    #[test] fn test_legacy_message_recent_blockhash_compatibility();
    #[test] fn test_legacy_message_instructions_compatibility();
}

mod v0_message_tests {
    #[test] fn test_v0_message_all_fields_compatibility();
    #[test] fn test_v0_message_address_table_lookups_compatibility();
    #[test] fn test_v0_message_vs_legacy_differences();
}

mod message_header_tests {
    #[test] fn test_message_header_signature_counts();
    #[test] fn test_message_header_readonly_accounts();
    #[test] fn test_message_header_binary_layout();
}
```

### **E. `tests/units/types/accounts_test.rs`**

```rust
mod pubkey_tests {
    // Binary compatibility with solana-pubkey
    #[test] fn test_pubkey_binary_representation();
    #[test] fn test_pubkey_serialization_compatibility();
    #[test] fn test_pubkey_from_array_compatibility();
    #[test] fn test_pubkey_to_bytes_compatibility();
    #[test] fn test_pubkey_as_ref_compatibility();

    // Edge cases
    #[test] fn test_pubkey_zero_bytes();
    #[test] fn test_pubkey_max_bytes();
    #[test] fn test_pubkey_random_bytes();
}

mod message_address_table_lookup_tests {
    #[test] fn test_address_table_lookup_serialization();
    #[test] fn test_writable_indexes_compatibility();
    #[test] fn test_readonly_indexes_compatibility();
    #[test] fn test_empty_indexes_handling();
}
```

### **F. `tests/units/types/instructions_test.rs`**

```rust
mod compiled_instruction_tests {
    #[test] fn test_compiled_instruction_serialization_compatibility();
    #[test] fn test_program_id_index_compatibility();
    #[test] fn test_accounts_array_compatibility();
    #[test] fn test_data_array_compatibility();

    // Edge cases
    #[test] fn test_empty_accounts_array();
    #[test] fn test_empty_data_array();
    #[test] fn test_large_data_array();
}
```

### **G. `tests/units/utils/message_deserializer_test.rs`**

```rust
mod message_visitor_tests {
    #[test] fn test_message_visitor_legacy_deserialization();
    #[test] fn test_message_visitor_v0_deserialization();
    #[test] fn test_message_visitor_version_prefix_handling();
    #[test] fn test_message_visitor_error_handling();
}

mod message_prefix_tests {
    #[test] fn test_message_prefix_legacy_detection();
    #[test] fn test_message_prefix_versioned_detection();
    #[test] fn test_message_prefix_invalid_version_handling();
}
```

---

## 🔗 3. INTEGRATION TESTS

### **A. `tests/integrations/lib_test.rs`**

```rust
mod end_to_end_compatibility_tests {
    // Full pipeline tests with real data
    #[test] fn test_full_decode_pipeline_with_1000_samples();
    #[test] fn test_decode_entries_vs_solana_entry_decode();
    #[test] fn test_cross_platform_compatibility();

    // Performance comparison tests
    #[test] fn test_decode_performance_vs_reference();
    #[test] fn test_memory_usage_compatibility();
}
```

### **B. `tests/integrations/types/*_test.rs`**

```rust
// Each integration test file focuses on cross-module compatibility
mod cross_module_compatibility_tests {
    #[test] fn test_entry_with_multiple_transaction_types();
    #[test] fn test_transaction_with_different_message_versions();
    #[test] fn test_message_with_various_instruction_types();
    #[test] fn test_full_object_graph_serialization();
}
```

---

## 📊 4. REAL-WORLD TEST DATA STRATEGY

### **A. Data Collection & Organization**

```
tests/data/
├── raw_shreds/           # Raw binary files from shred_collector
│   ├── shred_000001.bin
│   ├── shred_000002.bin
│   └── ... (up to 1000 files)
├── processed/            # Processed and categorized data
│   ├── small_entries/    # < 1KB entries
│   ├── medium_entries/   # 1KB - 10KB entries
│   ├── large_entries/    # > 10KB entries
│   └── edge_cases/       # Special cases
└── reference_outputs/    # Expected outputs from official crates
    ├── entries/
    ├── transactions/
    └── messages/
```

### **B. Data Loading Strategy**

1. **Lazy Loading**: Load data only when needed for specific tests
2. **Stratified Sampling**: Ensure diverse data representation
3. **Caching**: Cache processed data to speed up test runs
4. **Parallel Processing**: Process multiple samples concurrently

### **C. Test Data Categories**

1. **Size-based**: Small, medium, large entries
2. **Complexity-based**: Simple, complex, nested structures
3. **Version-based**: Legacy vs V0 messages
4. **Edge cases**: Empty, maximum size, malformed data

---

## ✅ 5. BINARY COMPATIBILITY VALIDATION STRATEGY

### **A. Multi-level Validation**

1. **Byte-level**: Direct binary comparison
2. **Structure-level**: Field-by-field comparison
3. **Semantic-level**: Functional equivalence
4. **Round-trip**: Serialize → Deserialize → Compare

### **B. Validation Pipeline**

```rust
fn validate_100_percent_compatibility<T, R>(
    custom_data: &T,
    reference_data: &R,
    test_bytes: &[u8]
) -> CompatibilityResult
where
    T: Serialize + for<'de> Deserialize<'de>,
    R: Serialize + for<'de> Deserialize<'de>,
{
    // 1. Serialization compatibility
    let custom_serialized = bincode::serialize(custom_data)?;
    let reference_serialized = bincode::serialize(reference_data)?;
    assert_eq!(custom_serialized, reference_serialized);

    // 2. Deserialization compatibility
    let custom_deserialized: T = bincode::deserialize(test_bytes)?;
    let reference_deserialized: R = bincode::deserialize(test_bytes)?;

    // 3. Round-trip compatibility
    let custom_roundtrip = bincode::serialize(&custom_deserialized)?;
    let reference_roundtrip = bincode::serialize(&reference_deserialized)?;
    assert_eq!(custom_roundtrip, reference_roundtrip);

    CompatibilityResult::Success
}
```

### **C. Automated Compatibility Testing**

```rust
// Macro for automated compatibility testing
macro_rules! test_compatibility_suite {
    ($custom_type:ty, $reference_type:ty, $test_data:expr) => {
        for (i, data) in $test_data.iter().enumerate() {
            validate_100_percent_compatibility::<$custom_type, $reference_type>(data)
                .unwrap_or_else(|e| panic!("Compatibility test {} failed: {}", i, e));
        }
    };
}
```

---

## 🎯 6. DELIVERABLES SUMMARY

### **Immediate Deliverables:**

1. ✅ **Helper Functions Structure** - Detailed plan above
2. ✅ **Reference Implementations** - Using official Solana crates
3. ✅ **Test Cases Specification** - Comprehensive test matrix
4. ✅ **Data Strategy** - Real-world test data organization
5. ✅ **Validation Framework** - 100% binary compatibility validation

### **Implementation Phases:**

1. **Phase 1**: Implement helper functions in `tests/common/`
2. **Phase 2**: Create reference implementations using official crates
3. **Phase 3**: Implement unit tests for each custom type
4. **Phase 4**: Implement integration tests for cross-module compatibility
5. **Phase 5**: Collect and organize real-world test data
6. **Phase 6**: Run comprehensive compatibility validation suite

---

## 📍 CURRENT STATUS

### **✅ Completed:**

-   **2025-06-03**: Tái cấu trúc hoàn toàn hệ thống testing với 19 file test trống
-   **2025-06-03**: Tạo cấu trúc thư mục tests/ theo best practices:
    -   `tests/common/` với 7 helper files (reference_implementations, compatibility_validators, test_data_loader, mock_data, fixtures, assertions, mod.rs)
    -   `tests/units/` với cấu trúc mirror từ `src/` (7 files)
    -   `tests/integrations/` với cấu trúc mirror từ `src/` (7 files)
-   **2025-06-03**: Cập nhật README.md với Testing Guidelines chi tiết
-   **2025-06-03**: Tạo kế hoạch chi tiết cho 100% binary compatibility testing
-   **2025-06-03**: **HOÀN THÀNH HELPER FUNCTIONS** - Implement đầy đủ helper functions trong `tests/common/`:
    -   `reference_implementations.rs` - Reference functions using official Solana crates (73 lines)
    -   `compatibility_validators.rs` - Binary compatibility validation functions với CompatibilityResult enum (172 lines)
    -   `test_data_loader.rs` - Real-world test data management với ShredDataset và ShredFile (245 lines)
    -   `assertions.rs` - Custom assertion macros cho binary compatibility testing (142 lines)
    -   `fixtures.rs` - Static test fixtures và organized test data với lazy loading (180 lines)
    -   `mock_data.rs` - Mock data generators với seed-based generation (272 lines)
-   **2025-06-03**: Tạo test data directory structure: `tests/data/{raw_shreds,processed,reference_outputs}`
-   **2025-06-03**: Verify helper functions hoạt động với 11 passing tests trong `tests/helper_functions_test.rs`

### **🔄 Currently Working On:**

-   Sẵn sàng implement unit tests cho từng module
-   Chuẩn bị collect real-world test data từ shredstream

### **📋 Next Steps:**

1. **✅ COMPLETED: Implement helper functions** trong `tests/common/` - DONE

2. **Collect real-world test data**:

    - Run `make collect-shreds` để thu thập 1000 shred samples
    - Organize data theo categories (size, complexity, version)
    - Create reference outputs using official crates

3. **Implement unit tests** starting with core types:
    - `tests/units/types/entries_test.rs` - Entry và ParsedEntry compatibility
    - `tests/units/types/accounts_test.rs` - Pubkey compatibility
    - `tests/units/lib_test.rs` - Main decode_entries function

### **🔧 Technical Decisions Made:**

-   Sử dụng official Solana crates trong dev-dependencies làm reference implementation
-   Implement byte-by-byte comparison cho 100% binary compatibility
-   Organize test data theo size và complexity categories
-   Sử dụng lazy loading và caching cho test data performance
-   Implement custom assertion macros cho compatibility testing
-   **Helper Functions Architecture**:
    -   Sử dụng `std::sync::OnceLock` cho lazy static initialization
    -   Implement CompatibilityResult enum cho detailed error reporting
    -   Seed-based mock data generation cho reproducible tests
    -   Modular design với separate concerns cho mỗi helper module
    -   Custom assertion macros với detailed error messages

---

## 📝 CONVERSATION TRACKING RULES

### **Update Guidelines:**

1. **End of Conversation**: Cập nhật section "Current Status" mỗi khi kết thúc cuộc trò chuyện
2. **Technical Decisions**: Ghi lại tất cả quyết định kỹ thuật mới trong "Technical Decisions Made"
3. **Progress Updates**: Cập nhật "Completed" và "Next Steps" với tiến độ thực tế
4. **Timestamp Format**: Sử dụng format `YYYY-MM-DD` cho tất cả timestamps
5. **Change Summary**: Thêm tóm tắt ngắn gọn những gì đã thảo luận vào cuối mỗi session

### **Required Updates:**

-   [x] Cập nhật "Completed" section với tasks hoàn thành
-   [x] Cập nhật "Currently Working On" với tasks đang làm
-   [x] Cập nhật "Next Steps" với priorities mới
-   [x] Ghi lại technical decisions và rationale
-   [x] Thêm timestamp và session summary

### **📅 Session Summary - 2025-06-03 15:14 (GMT+7):**

**Achievements:**

-   ✅ **HOÀN THÀNH 100% HELPER FUNCTIONS** cho binary compatibility testing system
-   ✅ Implement 6 helper modules với tổng cộng 1,084 lines of code
-   ✅ Tạo comprehensive testing infrastructure với lazy loading, seed-based generation
-   ✅ Verify tất cả functions hoạt động với 11/11 passing tests
-   ✅ Setup test data directory structure sẵn sàng cho real-world data

**Technical Decisions:**

-   Sử dụng `std::sync::OnceLock` cho lazy static initialization
-   Implement CompatibilityResult enum cho detailed error reporting
-   Seed-based mock data generation cho reproducible tests
-   Custom assertion macros với detailed error messages
-   Modular architecture với separation of concerns

**Next Phase Ready:**

-   Collect real-world test data với `make collect-shreds`
-   Implement unit tests cho từng module sử dụng helper functions
-   Run comprehensive binary compatibility tests với 1000 samples

---

## 🤖 AI CONTEXT

### **Project Overview:**

-   **Name**: Shredstream Decoder
-   **Type**: Rust library với WASM support
-   **Goal**: 100% binary compatibility với official Solana types
-   **Main Function**: `decode_entries()` - decode Solana entries từ shredstream data

### **Key Technologies:**

-   **Language**: Rust 2021 edition
-   **WASM**: wasm-bindgen, tsify cho TypeScript definitions
-   **Serialization**: bincode, serde
-   **Testing**: Custom test framework với real-world data validation
-   **Dependencies**: Official Solana crates (solana-entry, solana-transaction, etc.)

### **Established Preferences:**

-   **No tests, examples, or comments** in production code by default
-   **Error handling in separate files**
-   **Minimal APIs** with only essential functions
-   **Meaningful struct names** like 'ParsedEntry' over generic names
-   **Types organized in separate types directory**
-   **Environment-configurable parameters** with defaults
-   **Scientific separation** of testing vs WASM library dependencies

### **Project Structure:**

```
src/
├── lib.rs                    # Main library with decode_entries()
├── types/                    # Custom types mirroring Solana
│   ├── accounts.rs          # Pubkey, MessageAddressTableLookup
│   ├── entries.rs           # Entry, ParsedEntry
│   ├── instructions.rs      # CompiledInstruction
│   ├── messages.rs          # VersionedMessage, LegacyMessage, V0Message
│   ├── transactions.rs      # VersionedTransaction
│   └── mod.rs
└── utils/
    ├── message_deserializer.rs  # Custom deserialization logic
    └── mod.rs

tests/
├── common/                   # Shared testing utilities (5 files)
├── units/                    # Unit tests mirroring src/ (7 files)
└── integrations/            # Integration tests mirroring src/ (7 files)
```

### **Testing Strategy:**

-   **100% Binary Compatibility**: Byte-by-byte comparison với official Solana crates
-   **Real-world Data**: 1000 shred samples từ shredstream
-   **Reference Implementation**: Sử dụng official crates làm ground truth
-   **Comprehensive Coverage**: Unit + Integration + End-to-end testing
-   **Performance Validation**: Memory usage và speed comparison

### **Tools & Commands:**

-   **Build**: `make build`, `make build-wasm`
-   **Testing**: `make test`, `make test-all`
-   **Data Collection**: `make collect-shreds`
-   **Development**: `make fmt`, `make lint`, `make clean`
